-- 软删除 moe_book_online_question 表中重复的 client 和 pet question 记录
-- 保留最近的一条记录（基于 update_time 和 id）
-- 重复判断基于：business_id + question + type 的组合
-- 使用软删除：将 status 设置为 0，而不是物理删除记录

-- 方法1: 使用 ROW_NUMBER() 窗口函数和 CTE（推荐）
WITH duplicate_records AS (
    SELECT
        id,
        business_id,
        question,
        type,
        update_time,
        create_time,
        ROW_NUMBER() OVER (
            PARTITION BY business_id, question, type
            ORDER BY update_time DESC, create_time DESC, id DESC
        ) as rn
    FROM moe_book_online_question
    WHERE status = 1  -- 只处理正常状态的记录
)
UPDATE moe_book_online_question
SET status = 0, update_time = UNIX_TIMESTAMP()
WHERE id IN (
    SELECT id
    FROM duplicate_records
    WHERE rn > 1
);

-- 方法2: 使用子查询和窗口函数（适用于不支持CTE的数据库）
UPDATE moe_book_online_question
SET status = 0, update_time = UNIX_TIMESTAMP()
WHERE id NOT IN (
    SELECT * FROM (
        SELECT
            id
        FROM (
            SELECT
                id,
                ROW_NUMBER() OVER (
                    PARTITION BY business_id, question, type
                    ORDER BY update_time DESC, create_time DESC, id DESC
                ) as rn
            FROM moe_book_online_question
            WHERE status = 1
        ) ranked
        WHERE rn = 1
    ) keep_records
)
AND status = 1;

-- 方法3: 使用 EXISTS 子查询（性能较好的替代方案）
UPDATE moe_book_online_question t1
SET status = 0, update_time = UNIX_TIMESTAMP()
WHERE EXISTS (
    SELECT 1
    FROM moe_book_online_question t2
    WHERE t1.business_id = t2.business_id
    AND t1.question = t2.question
    AND t1.type = t2.type
    AND t1.status = 1 AND t2.status = 1
    AND (
        t2.update_time > t1.update_time
        OR (t2.update_time = t1.update_time AND t2.create_time > t1.create_time)
        OR (t2.update_time = t1.update_time AND t2.create_time = t1.create_time AND t2.id > t1.id)
    )
)
AND t1.status = 1;

-- 方法4: 使用临时表的安全方法
-- 第一步：创建临时表保存要保留的记录
CREATE TEMPORARY TABLE temp_keep_records AS
SELECT
    id
FROM (
    SELECT
        id,
        ROW_NUMBER() OVER (
            PARTITION BY business_id, question, type
            ORDER BY update_time DESC, create_time DESC, id DESC
        ) as rn
    FROM moe_book_online_question
    WHERE status = 1
) ranked
WHERE rn = 1;

-- 第二步：软删除不在保留列表中的记录
UPDATE moe_book_online_question
SET status = 0, update_time = UNIX_TIMESTAMP()
WHERE id NOT IN (SELECT id FROM temp_keep_records)
AND status = 1;

-- 第三步：清理临时表
DROP TEMPORARY TABLE temp_keep_records;

-- ========== 恢复操作（如果需要回滚） ==========

-- 恢复所有在指定时间后被软删除的记录
-- 请将 YOUR_TIMESTAMP 替换为执行软删除操作的时间戳
/*
UPDATE moe_book_online_question
SET status = 1
WHERE status = 0
AND update_time >= YOUR_TIMESTAMP;
*/

-- 或者根据具体的ID列表恢复（更精确的方式）
/*
UPDATE moe_book_online_question
SET status = 1
WHERE id IN (1, 2, 3, ...);  -- 替换为实际的ID列表
*/

-- ========== 查询验证和分析 ==========

-- 1. 查看当前重复记录情况（执行删除前运行）
SELECT
    business_id,
    question,
    type,
    COUNT(*) as record_count,
    MAX(update_time) as latest_update_time,
    MAX(create_time) as latest_create_time,
    GROUP_CONCAT(id ORDER BY update_time DESC, create_time DESC, id DESC) as all_ids
FROM moe_book_online_question
WHERE status = 1
GROUP BY business_id, question, type
HAVING COUNT(*) > 1
ORDER BY record_count DESC, business_id, type;

-- 2. 查看将要保留的记录（执行删除前预览）
SELECT
    id,
    business_id,
    question,
    type,
    update_time,
    create_time,
    FROM_UNIXTIME(update_time) as update_datetime,
    FROM_UNIXTIME(create_time) as create_datetime
FROM (
    SELECT
        id,
        business_id,
        question,
        type,
        update_time,
        create_time,
        ROW_NUMBER() OVER (
            PARTITION BY business_id, question, type
            ORDER BY update_time DESC, create_time DESC, id DESC
        ) as rn
    FROM moe_book_online_question
    WHERE status = 1
) ranked
WHERE rn = 1
AND (business_id, question, type) IN (
    SELECT business_id, question, type
    FROM moe_book_online_question
    WHERE status = 1
    GROUP BY business_id, question, type
    HAVING COUNT(*) > 1
)
ORDER BY business_id, type, question;

-- 3. 查看将要软删除的记录（执行软删除前预览）
SELECT
    id,
    business_id,
    question,
    type,
    update_time,
    create_time,
    FROM_UNIXTIME(update_time) as update_datetime,
    FROM_UNIXTIME(create_time) as create_datetime,
    '将被软删除' as action
FROM (
    SELECT
        id,
        business_id,
        question,
        type,
        update_time,
        create_time,
        ROW_NUMBER() OVER (
            PARTITION BY business_id, question, type
            ORDER BY update_time DESC, create_time DESC, id DESC
        ) as rn
    FROM moe_book_online_question
    WHERE status = 1
) ranked
WHERE rn > 1
ORDER BY business_id, type, question, rn;

-- 4. 统计信息
SELECT
    '总记录数' as metric,
    COUNT(*) as count
FROM moe_book_online_question
WHERE status = 1

UNION ALL

SELECT
    '重复组数' as metric,
    COUNT(*) as count
FROM (
    SELECT business_id, question, type
    FROM moe_book_online_question
    WHERE status = 1
    GROUP BY business_id, question, type
    HAVING COUNT(*) > 1
) duplicate_groups

UNION ALL

SELECT
    '重复记录总数' as metric,
    SUM(record_count) as count
FROM (
    SELECT COUNT(*) as record_count
    FROM moe_book_online_question
    WHERE status = 1
    GROUP BY business_id, question, type
    HAVING COUNT(*) > 1
) duplicate_counts

UNION ALL

SELECT
    '将要软删除的记录数' as metric,
    SUM(record_count - 1) as count
FROM (
    SELECT COUNT(*) as record_count
    FROM moe_book_online_question
    WHERE status = 1
    GROUP BY business_id, question, type
    HAVING COUNT(*) > 1
) duplicate_counts

UNION ALL

SELECT
    '已软删除的记录数' as metric,
    COUNT(*) as count
FROM moe_book_online_question
WHERE status = 0;
